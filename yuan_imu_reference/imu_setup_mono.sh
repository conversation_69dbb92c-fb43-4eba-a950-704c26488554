#!/bin/bash

# IMU Device Setup Script
# This script configures both gyroscope (device1) and accelerometer (device2) devices
# Run this script as root before starting the IMU sensor node
# Usage: sudo ./setup_imu_devices.sh [options]

set -e  # Exit on any error

# Default configuration values
SAMPLING_FREQ="200"
ACC_SCALE="0.001197101"
GYRO_SCALE="0.001065264"
# GYRO_SCALE="0.000266316"
TIMESTAMP_CLOCK="monotonic"
BUFFER_LENGTH="480"
ENABLE_VALUE="1"
DISABLE_VALUE="0"

# Device paths
GYRO_DEVICE_PATH="/dev/iio:device1"
ACCEL_DEVICE_PATH="/dev/iio:device2"

# Gyroscope device (device1) file paths
GYRO_BASE_PATH="/sys/bus/iio/devices/iio:device1"
GYRO_BUFF_ENABLE_FILE="${GYRO_BASE_PATH}/buffer/enable"
GYRO_SAMPLING_FREQ_FILE="${GYRO_BASE_PATH}/sampling_frequency"
GYRO_SCALE_FILE="${GYRO_BASE_PATH}/in_anglvel_scale"
GYRO_SCAN_ELEMENT_X_FILE="${GYRO_BASE_PATH}/scan_elements/in_anglvel_x_en"
GYRO_SCAN_ELEMENT_Y_FILE="${GYRO_BASE_PATH}/scan_elements/in_anglvel_y_en"
GYRO_SCAN_ELEMENT_Z_FILE="${GYRO_BASE_PATH}/scan_elements/in_anglvel_z_en"
GYRO_SCAN_ELEMENT_TIMESTAMP_FILE="${GYRO_BASE_PATH}/scan_elements/in_timestamp_en"
GYRO_TIMESTAMP_CLOCK_FILE="${GYRO_BASE_PATH}/current_timestamp_clock"
GYRO_BUFFER_LENGTH_FILE="${GYRO_BASE_PATH}/buffer/length"

# Accelerometer device (device2) file paths
ACCEL_BASE_PATH="/sys/bus/iio/devices/iio:device2"
ACCEL_BUFF_ENABLE_FILE="${ACCEL_BASE_PATH}/buffer/enable"
ACCEL_SAMPLING_FREQ_FILE="${ACCEL_BASE_PATH}/sampling_frequency"
ACCEL_SCALE_FILE="${ACCEL_BASE_PATH}/in_accel_scale"
ACCEL_SCAN_ELEMENT_X_FILE="${ACCEL_BASE_PATH}/scan_elements/in_accel_x_en"
ACCEL_SCAN_ELEMENT_Y_FILE="${ACCEL_BASE_PATH}/scan_elements/in_accel_y_en"
ACCEL_SCAN_ELEMENT_Z_FILE="${ACCEL_BASE_PATH}/scan_elements/in_accel_z_en"
ACCEL_SCAN_ELEMENT_TIMESTAMP_FILE="${ACCEL_BASE_PATH}/scan_elements/in_timestamp_en"
ACCEL_TIMESTAMP_CLOCK_FILE="${ACCEL_BASE_PATH}/current_timestamp_clock"
ACCEL_BUFFER_LENGTH_FILE="${ACCEL_BASE_PATH}/buffer/length"

# Flags
SETUP_GYRO=true
SETUP_ACCEL=true
VERBOSE=true
DRY_RUN=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to write to file with error checking
write_to_file() {
    local file_path="$1"
    local value="$2"
    local description="$3"
    
    if [ "$DRY_RUN" = true ]; then
        print_info "DRY RUN: Would write '$value' to $file_path ($description)"
        return 0
    fi
    
    if [ ! -f "$file_path" ]; then
        print_error "File does not exist: $file_path"
        return 1
    fi
    
    print_info "Writing '$value' to $file_path ($description)"
    
    if echo "$value" > "$file_path" 2>/dev/null; then
        return 0
    else
        print_error "Failed to write to $file_path"
        return 1
    fi
}

# Function to check if device exists
check_device() {
    local device_path="$1"
    local device_name="$2"
    
    if [ ! -e "$device_path" ]; then
        print_error "$device_name device not found at $device_path"
        return 1
    fi
    
    print_success "$device_name device found at $device_path"
    return 0
}

# Function to setup gyroscope device
setup_gyroscope() {
    print_info "Setting up gyroscope device (device1)..."
    
    # Check if device exists
    if ! check_device "$GYRO_DEVICE_PATH" "Gyroscope"; then
        return 1
    fi
    
    # Disable buffer first
    write_to_file "$GYRO_BUFF_ENABLE_FILE" "$DISABLE_VALUE" "disable gyro buffer" || return 1
    
    # Set sampling frequency
    write_to_file "$GYRO_SAMPLING_FREQ_FILE" "$SAMPLING_FREQ" "gyro sampling frequency" || return 1
    
    # Set gyroscope scale
    write_to_file "$GYRO_SCALE_FILE" "$GYRO_SCALE" "gyro scale" || return 1
    
    # Enable scan elements
    write_to_file "$GYRO_SCAN_ELEMENT_X_FILE" "$ENABLE_VALUE" "gyro X scan element" || return 1
    write_to_file "$GYRO_SCAN_ELEMENT_Y_FILE" "$ENABLE_VALUE" "gyro Y scan element" || return 1
    write_to_file "$GYRO_SCAN_ELEMENT_Z_FILE" "$ENABLE_VALUE" "gyro Z scan element" || return 1
    write_to_file "$GYRO_SCAN_ELEMENT_TIMESTAMP_FILE" "$ENABLE_VALUE" "gyro timestamp scan element" || return 1
    
    # Set timestamp clock
    write_to_file "$GYRO_TIMESTAMP_CLOCK_FILE" "$TIMESTAMP_CLOCK" "gyro timestamp clock" || return 1
    
    # Set buffer length
    write_to_file "$GYRO_BUFFER_LENGTH_FILE" "$BUFFER_LENGTH" "gyro buffer length" || return 1
    
    print_success "Gyroscope device configured successfully"
    return 0
}

# Function to setup accelerometer device
setup_accelerometer() {
    print_info "Setting up accelerometer device (device2)..."
    
    # Check if device exists
    if ! check_device "$ACCEL_DEVICE_PATH" "Accelerometer"; then
        return 1
    fi
    
    # Disable buffer first
    write_to_file "$ACCEL_BUFF_ENABLE_FILE" "$DISABLE_VALUE" "disable accel buffer" || return 1
    
    # Set sampling frequency
    write_to_file "$ACCEL_SAMPLING_FREQ_FILE" "$SAMPLING_FREQ" "accel sampling frequency" || return 1
    
    # Set accelerometer scale
    write_to_file "$ACCEL_SCALE_FILE" "$ACC_SCALE" "accel scale" || return 1
    
    # Enable scan elements
    write_to_file "$ACCEL_SCAN_ELEMENT_X_FILE" "$ENABLE_VALUE" "accel X scan element" || return 1
    write_to_file "$ACCEL_SCAN_ELEMENT_Y_FILE" "$ENABLE_VALUE" "accel Y scan element" || return 1
    write_to_file "$ACCEL_SCAN_ELEMENT_Z_FILE" "$ENABLE_VALUE" "accel Z scan element" || return 1
    write_to_file "$ACCEL_SCAN_ELEMENT_TIMESTAMP_FILE" "$ENABLE_VALUE" "accel timestamp scan element" || return 1
    
    # Set timestamp clock
    write_to_file "$ACCEL_TIMESTAMP_CLOCK_FILE" "$TIMESTAMP_CLOCK" "accel timestamp clock" || return 1
    
    # Set buffer length
    write_to_file "$ACCEL_BUFFER_LENGTH_FILE" "$BUFFER_LENGTH" "accel buffer length" || return 1
    
    print_success "Accelerometer device configured successfully"
    return 0
}

# Function to enable buffers
enable_buffers() {
    print_info "Enabling device buffers..."
    
    if [ "$SETUP_GYRO" = true ]; then
        write_to_file "$GYRO_BUFF_ENABLE_FILE" "$ENABLE_VALUE" "enable gyro buffer" || return 1
        print_success "Gyroscope buffer enabled"
    fi
    
    if [ "$SETUP_ACCEL" = true ]; then
        write_to_file "$ACCEL_BUFF_ENABLE_FILE" "$ENABLE_VALUE" "enable accel buffer" || return 1
        print_success "Accelerometer buffer enabled"
    fi
    
    return 0
}

# Function to disable buffers
disable_buffers() {
    print_info "Disabling device buffers..."
    
    if [ "$SETUP_GYRO" = true ]; then
        write_to_file "$GYRO_BUFF_ENABLE_FILE" "$DISABLE_VALUE" "disable gyro buffer"
        print_success "Gyroscope buffer disabled"
    fi
    
    if [ "$SETUP_ACCEL" = true ]; then
        write_to_file "$ACCEL_BUFF_ENABLE_FILE" "$DISABLE_VALUE" "disable accel buffer"
        print_success "Accelerometer buffer disabled"
    fi
}

# Function to set device permissions
set_permissions() {
    print_info "Setting device permissions..."
    
    if [ "$SETUP_GYRO" = true ] && [ -e "$GYRO_DEVICE_PATH" ]; then
        chmod 666 "$GYRO_DEVICE_PATH" 2>/dev/null || print_warning "Could not set permissions for $GYRO_DEVICE_PATH"
        print_success "Gyroscope device permissions set"
    fi
    
    if [ "$SETUP_ACCEL" = true ] && [ -e "$ACCEL_DEVICE_PATH" ]; then
        chmod 666 "$ACCEL_DEVICE_PATH" 2>/dev/null || print_warning "Could not set permissions for $ACCEL_DEVICE_PATH"
        print_success "Accelerometer device permissions set"
    fi
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --gyro-only         Setup only gyroscope device"
    echo "  --accel-only        Setup only accelerometer device"
    echo "  --both              Setup both devices (default)"
    echo "  --disable           Disable buffers instead of enabling"
    echo "  --dry-run           Show what would be done without making changes"
    echo "  --quiet, -q         Reduce output verbosity"
    echo "  --help, -h          Show this help message"
    echo ""
    echo "Configuration values:"
    echo "  --sampling-freq     Sampling frequency (default: $SAMPLING_FREQ)"
    echo "  --acc-scale         Accelerometer scale (default: $ACC_SCALE)"
    echo "  --gyro-scale        Gyroscope scale (default: $GYRO_SCALE)"
    echo "  --buffer-length     Buffer length (default: $BUFFER_LENGTH)"
    echo ""
    echo "Examples:"
    echo "  sudo $0                    # Setup both devices with default settings"
    echo "  sudo $0 --gyro-only        # Setup only gyroscope"
    echo "  sudo $0 --disable          # Disable both device buffers"
    echo "  sudo $0 --dry-run          # Show what would be done"
}

# Parse command line arguments
DISABLE_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --gyro-only)
            SETUP_GYRO=true
            SETUP_ACCEL=false
            shift
            ;;
        --accel-only)
            SETUP_GYRO=false
            SETUP_ACCEL=true
            shift
            ;;
        --both)
            SETUP_GYRO=true
            SETUP_ACCEL=true
            shift
            ;;
        --disable)
            DISABLE_MODE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --quiet|-q)
            VERBOSE=false
            shift
            ;;
        --sampling-freq)
            SAMPLING_FREQ="$2"
            shift 2
            ;;
        --acc-scale)
            ACC_SCALE="$2"
            shift 2
            ;;
        --gyro-scale)
            GYRO_SCALE="$2"
            shift 2
            ;;
        --buffer-length)
            BUFFER_LENGTH="$2"
            shift 2
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "IMU Device Setup Script"
    echo "======================="
    
    # Check if running as root
    if [ "$EUID" -ne 0 ] && [ "$DRY_RUN" = false ]; then
        print_error "This script must be run as root (use sudo)"
        print_info "Or use --dry-run to see what would be done"
        exit 1
    fi
    
    if [ "$DRY_RUN" = true ]; then
        print_warning "DRY RUN MODE - No changes will be made"
    fi
    
    print_info "Configuration:"
    print_info "  Gyroscope: $([ "$SETUP_GYRO" = true ] && echo "ENABLED" || echo "DISABLED")"
    print_info "  Accelerometer: $([ "$SETUP_ACCEL" = true ] && echo "ENABLED" || echo "DISABLED")"
    print_info "  Sampling frequency: $SAMPLING_FREQ Hz"
    print_info "  Accelerometer scale: $ACC_SCALE"
    print_info "  Gyroscope scale: $GYRO_SCALE"
    print_info "  Buffer length: $BUFFER_LENGTH"
    echo ""
    
    if [ "$DISABLE_MODE" = true ]; then
        print_info "Disabling IMU device buffers..."
        disable_buffers
        print_success "IMU devices disabled successfully"
    else
        # Setup devices
        if [ "$SETUP_GYRO" = true ]; then
            setup_gyroscope || exit 1
        fi
        
        if [ "$SETUP_ACCEL" = true ]; then
            setup_accelerometer || exit 1
        fi
        
        # Set permissions for non-root access
        set_permissions
        
        # Enable buffers
        enable_buffers || exit 1
        
        print_success "IMU devices configured and ready for use"
        print_info "You can now run the IMU sensor node as a regular user"
    fi
}

# Run main function
main "$@"
