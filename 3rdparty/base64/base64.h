#pragma once

#ifdef _WIN32
#   define RPOS_DEPS_BASE64_MODULE_EXPORT __declspec(dllexport)
#   define RPOS_DEPS_BASE64_MODULE_IMPORT __declspec(dllimport)
#else
#   define RPOS_DEPS_BASE64_MODULE_EXPORT
#   define RPOS_DEPS_BASE64_MODULE_IMPORT
#endif

#ifdef RPOS_DEPS_BASE64_DLL
#   ifdef RPOS_DEPS_BASE64_EXPORT
#       define RPOS_DEPS_BASE64_API RPOS_DEPS_BASE64_MODULE_EXPORT
#   else
#       define RPOS_DEPS_BASE64_API RPOS_DEPS_BASE64_MODULE_IMPORT
#   endif
#else
#   define RPOS_DEPS_BASE64_API
#endif

#include <string>
#include <vector>

typedef unsigned char uint8_t;

namespace rp { namespace system { namespace util {

    RPOS_DEPS_BASE64_API void encodeBase64(unsigned char const* bytes_to_encode, std::string &outString, unsigned int in_len);
    RPOS_DEPS_BASE64_API bool decodeBase64(const char* inString, size_t size, std::vector<uint8_t> &outData);
    RPOS_DEPS_BASE64_API bool decodeBase64(const std::string &inString, std::vector<uint8_t> &outData);

}}}