#include "base64.h"
#include <iostream>


namespace rp { namespace system { namespace util {


#if 1
// new implemenation to improve performance
// 3-4x faster on X1000 target

// code from http://web.mit.edu/freebsd/head/contrib/wpa/src/utils/base64.c
// according to the https://stackoverflow.com/questions/342409/how-do-i-base64-encode-decode-in-c
// the implemantion has the best performance among those without the help of special instruction sets or GPU
/*

Here is the time (in microseconds) to encode 32K of data using the different algorithms:

    jounimalinen                25.1544  <-- we used
    apache                      25.5309
    NibbleAndAHalf              38.4165
    internetsoftwareconsortium  48.2879
    polfosol                    48.7955
    wikibooks_org_c             51.9659
    gnome                       74.8188
    elegantdice                 118.899
    libb64                      120.601
    manuelmartinez              120.801
    arduino                     126.262
    daedalusalpha               126.473
    CppCodec                    151.866
    wikibooks_org_cpp           343.2
    adp_gmbh                    381.523
    LihO                        406.693
    libcurl                     3246.39
    user152949                  4828.21

*/

/*
 * Base64 encoding/decoding (RFC1341)
 * Copyright (c) 2005-2011, <PERSON><PERSON> <<EMAIL>>
 *
 * This software may be distributed under the terms of the BSD license.
 * See README for more details.
 */

static const unsigned char base64_table[65] =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";


void encodeBase64(unsigned char const* src, std::string &outStr, unsigned int len) {
    unsigned char *out, *pos;
    const unsigned char *end, *in;

    size_t olen;

    olen = 4 * ((len + 2) / 3); /* 3-byte blocks to 4-byte */

    if (!src || len == 0) return;
    if (olen < len)
        return; /* integer overflow */

    outStr.resize(olen);
    out = (unsigned char*)&outStr[0];

    end = src + len;
    in = src;
    pos = out;
    while (end - in >= 3) {
        *pos++ = base64_table[in[0] >> 2];
        *pos++ = base64_table[((in[0] & 0x03) << 4) | (in[1] >> 4)];
        *pos++ = base64_table[((in[1] & 0x0f) << 2) | (in[2] >> 6)];
        *pos++ = base64_table[in[2] & 0x3f];
        in += 3;
    }

    if (end - in) {
        *pos++ = base64_table[in[0] >> 2];
        if (end - in == 1) {
            *pos++ = base64_table[(in[0] & 0x03) << 4];
            *pos++ = '=';
        }
        else {
            *pos++ = base64_table[((in[0] & 0x03) << 4) |
                (in[1] >> 4)];
            *pos++ = base64_table[(in[1] & 0x0f) << 2];
        }
        *pos++ = '=';
    }
}


// for the decoder, we chose another implementation
// https://stackoverflow.com/questions/180947/base64-decode-snippet-in-c/13935718
/*
    polfosol                    45.2335 <- we use this
    wikibooks_org_c             74.7347
    apache                      77.1438
    libb64                      100.332
    gnome                       114.511
    manuelmartinez              126.579
    elegantdice                 138.514
    daedalusalpha               151.561
    jounimalinen                206.163
    arduino                     335.95
    wikibooks_org_cpp           350.437
    CppCodec                    526.187
    internetsoftwareconsortium  862.833
    libcurl                     1280.27
    LihO                        1852.4
    adp_gmbh                    1934.43
    user152949                  5332.87

*/

static const int B64index[256] = { 0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0, 62, 63, 62, 62, 63, 52, 53, 54, 55,
56, 57, 58, 59, 60, 61,  0,  0,  0,  0,  0,  0,  0,  0,  1,  2,  3,  4,  5,  6,
7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,  0,
0,  0,  0, 63,  0, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51 };

bool decodeBase64(const char* data, size_t len, std::vector<uint8_t> &outData)
{
    const unsigned char* p = (const unsigned char*)data;
    outData.clear();

    if (!data) return false;
    if (!len) {
        return false;
    }


    size_t j = 0,
        pad1 = len % 4 || p[len - 1] == '=',
        pad2 = pad1 && (len % 4 > 2 || p[len - 2] != '=');
    const size_t last = (len - pad1) / 4 << 2;

    outData.resize(last / 4 * 3 + pad1 + pad2);
    unsigned char *str = (unsigned char*)&outData[0];

    for (size_t i = 0; i < last; i += 4)
    {
        int n = B64index[p[i]] << 18 | B64index[p[i + 1]] << 12 | B64index[p[i + 2]] << 6 | B64index[p[i + 3]];
        str[j++] = n >> 16;
        str[j++] = n >> 8 & 0xFF;
        str[j++] = n & 0xFF;
    }
    if (pad1)
    {
        int n = B64index[p[last]] << 18 | B64index[p[last + 1]] << 12;
        str[j++] = n >> 16;
        if (pad2)
        {
            n |= B64index[p[last + 2]] << 6;
            str[j++] = n >> 8 & 0xFF;
        }
    }
    return true;
}


#else
const unsigned char alphabet[64+1] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

/*---------------------------------------------------------------
					encodeBase64
---------------------------------------------------------------*/

void encodeBase64(unsigned char const* bytes_to_encode, std::string &outString, unsigned int in_len) {
  int i = 0;
  int j = 0;
  unsigned char char_array_3[3];
  unsigned char char_array_4[4];

  while (in_len--) {
    char_array_3[i++] = *(bytes_to_encode++);
    if (i == 3) {
      char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
      char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
      char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
      char_array_4[3] = char_array_3[2] & 0x3f;

      for(i = 0; (i <4) ; i++)
        outString += alphabet[char_array_4[i]];
      i = 0;
    }
  }

  if (i)
  {
    for(j = i; j < 3; j++)
      char_array_3[j] = '\0';

    char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
    char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
    char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
    char_array_4[3] = char_array_3[2] & 0x3f;

    for (j = 0; (j < i + 1); j++)
      outString += alphabet[char_array_4[j]];

    while((i++ < 3))
      outString += '=';
  }
}

/*---------------------------------------------------------------
					decodeBase64
---------------------------------------------------------------*/
bool decodeBase64(const char* inString, size_t size, std::vector<uint8_t> &outData)
{
	static bool inalphabet[256];
	static char decoder[256];

	static bool tablesBuilt = false;

	if (!tablesBuilt)
	{
		tablesBuilt = true;
		for (int i = (sizeof(alphabet)) - 1; i >= 0 ; i--)
		{
			inalphabet[alphabet[i]] = 1;
			decoder[alphabet[i]] = i;
		}
	}

    outData.clear();
	outData.reserve( size * 6 / 8 );

	int errors = 0;

	int char_count = 0;
	int bits = 0;
	bool finish_flag_found = false;

	for (size_t i=0;i<size;i++)
	{
		const unsigned char c = inString[i];

		if (c == '=')
		{
			finish_flag_found = true;
			break;
		}
		if (!inalphabet[c])
			continue;

		bits += decoder[c];
		char_count++;
		if (char_count == 4)
		{
			outData.push_back((bits >> 16));
			outData.push_back(((bits >> 8) & 0xff));
			outData.push_back((bits & 0xff));
			bits = 0;
			char_count = 0;
		}
		else
			bits <<= 6;
	}

	if (!finish_flag_found)
	{
		if (char_count)
		{
			std::cerr << "[decodeBase64] ERROR: base64 encoding incomplete" << std::endl;
			errors++;
		}
	}
	else
	{ /* c == '=' */
		switch (char_count)
		{
		case 1:
			std::cerr << "[decodeBase64] ERROR: base64 encoding incomplete, at least 2 bits missing" << std::endl;
			errors++;
			break;
		case 2:
			outData.push_back((bits >> 10));
			break;
		case 3:
			outData.push_back((bits >> 16));
			outData.push_back(((bits >> 8) & 0xff));
			break;
		}
	}

	return errors==0;
}
#endif
bool decodeBase64(const std::string& inString, std::vector<uint8_t> &outData)
{
    return decodeBase64(inString.c_str(), inString.size(), outData);
}



}}}
