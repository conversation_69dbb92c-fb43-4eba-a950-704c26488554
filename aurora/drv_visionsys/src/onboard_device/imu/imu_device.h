/*
 *    Copyright 2013 - 2019 SLAMTEC Co., Ltd.
 *    AuroraCore Vision System
 *
 *    Host driver for the vision system
 */
#pragma once

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/time.h>
#include <memory>
#include <list>
#include <deque>
#include <utility>

// Forward declaration for geometry_msgs::msg::Vector3 (simplified for this context)
namespace geometry_msgs { namespace msg {
    struct Vector3 {
        double x, y, z;
    };
}}

#include "infra.h"
#include "ev/event_dispatcher.h"
#include "hal/thread.h"
#include "hal/locker.h"
#include "hal/event.h"
#include "auroravision_configuration.h"
#include "auroravision_events.h"


namespace rp{ namespace auroracore { namespace drv{

enum
{
    IMU_SENSOR_OUTPUT_RATE              = 200,  //  hz
    IMU_SENSOR_ACC_SENSITIVITY          = 4,    // g
    IMU_SENSOR_GYRO_SENSITIVITY         = 2000, // dps
};

// Separate structures for ICM42688 dual device setup
struct gyro_device_buffer_frame
{
    uint8_t  gyro_x_h;
    uint8_t  gyro_x_l;
    uint8_t  gyro_y_h;
    uint8_t  gyro_y_l;
    uint8_t  gyro_z_h;
    uint8_t  gyro_z_l;
    uint8_t  padding[2];
    uint8_t  timestamp[8];
};

struct accel_device_buffer_frame
{
    uint8_t  acc_x_h;
    uint8_t  acc_x_l;
    uint8_t  acc_y_h;
    uint8_t  acc_y_l;
    uint8_t  acc_z_h;
    uint8_t  acc_z_l;
    uint8_t  padding[2];
    uint8_t  timestamp[8];
};

// Legacy combined structure for backward compatibility
struct imu_device_buffer_frame
{
    uint8_t  acc_x_h;
    uint8_t  acc_x_l;
    uint8_t  acc_y_h;
    uint8_t  acc_y_l;
    uint8_t  acc_z_h;
    uint8_t  acc_z_l;
    uint8_t  gyro_x_h;
    uint8_t  gyro_x_l;
    uint8_t  gyro_y_h;
    uint8_t  gyro_y_l;
    uint8_t  gyro_z_h;
    uint8_t  gyro_z_l;
    uint8_t  padding[4];
    uint8_t  timestamp[8];
};

static const std::string IMU_DEFAULT_GYRO_DEVICE_FILE = "/dev/iio:device1";
static const std::string IMU_DEFAULT_ACCEL_DEVICE_FILE = "/dev/iio:device2";
static const std::string IMU_DEFAULT_DEVICE_FILE = "/dev/iio:device1"; // For backward compatibility



class _single_thread ImuSensor
{
public:
    ImuSensor(rp::ev::EventDispatcher<AuroraVisionEvent>& dis, std::string gyro_dev = IMU_DEFAULT_GYRO_DEVICE_FILE, std::string accel_dev = IMU_DEFAULT_ACCEL_DEVICE_FILE);

    ~ImuSensor();

    u_result open();
    u_result close();
    u_result streamOn();
    bool isSensorOpened() {return isSensorOpened_;}

    size_t getProfileCnt();
    size_t getProfileDescSize();
    auroravision_imustream_desc_t * getProfiles();
    u_result  setActiveStreamProfile(size_t id);
    //u_result  getStreamStatus(bool &, _u32 &, _u32 & );

    size_t getActiveProfileId();
    size_t getDefaultProfileId();

private:
    // Utility function for timestamp synchronization
    uint64_t findClosestTimestamp(uint64_t target_timestamp, const std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>>& data_queue);


private:
    // event publish thread
    u_result    eventDispProc_();

    // fetch sensor frame thread
    u_result    frameFetchProc_();

    void resetDispatchEventBuffer_();
    void resetFrameEventBuffer_();
    u_result publishSensorEvent(AuroraVisionEvent& evt);

//private:
public:
    
    // event publish related
    rp::ev::EventDispatcher<AuroraVisionEvent>&     eventDispatcher_;
    rp::auroracore::drv::AuroraVisionEvent          dispatchEvent_;
    rp::hal::Locker                                 opLocker_;
    rp::hal::Locker                                 evtdispLocker_;
    rp::hal::Event                                  pendingEvent_;
    size_t                                          eventIDCounter_;
    std::list<AuroraVisionEvent>                    pendingEventQueue_;   
    bool                                            eventDisptchStarted_;
    rp::hal::Thread                                 eventDispatchThread_;


    // sensor frame fetch related
    rp::auroracore::drv::AuroraVisionEvent          frameEvent_;
    bool                                            sensroFrameStarted_;
    rp::hal::Thread                                 sensroFrameThread_;

    bool                                            isSensorOpened_;
    size_t                                          default_profile_;
    size_t                                          active_profile_;

    // Dual device support for ICM42688
    std::string                                     gyro_dev_;
    std::string                                     accel_dev_;
    int                                             gyro_dev_fd_;
    int                                             accel_dev_fd_;
    uint64_t                                        last_gyro_timestamp_;
    uint64_t                                        last_accel_timestamp_;

    // Legacy single device support
    std::string                                     dev_;
    int                                             dev_fd_;
    uint64_t                                        last_frame_timestamp_;

    // Data queues for timestamp synchronization
    std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>> gyro_data_queue_;
    std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>> accel_data_queue_;

    // bool  enabled;
    // _u32  status;
    // _u32  errorCode;

};


}}}
