/*
 *    Copyright 2013 - 2019 SLAMTEC Co., Ltd.
 *    AuroraCore Vision System
 *
 *    Host driver for the vision system
 */
#include "imu_device.h"
#include <chrono>
#include "drv_auroravision.h"
#include <fcntl.h>

#include <time.h>
#include <signal.h>
#include <poll.h>

// #include <cstdlib>
// #include <cstdint>
// #include <cstdio>

// #include <string.h>
// #include <poll.h>
// #include <errno.h>
// #include <fcntl.h>
// #include <unistd.h>
#include "onboard_device/onboard_device.h"

namespace rp{ namespace auroracore { namespace drv{

#define POLL_TIMEOUT      1000
#define ACC_Q16_FACTOR    (IMU_SENSOR_ACC_SENSITIVITY * (1<<16) / (1<<15))
#define GYRO_Q16_FACTOR   (IMU_SENSOR_GYRO_SENSITIVITY * (1<<16) / (1<<15))

#define CLEAR(x) memset(&(x), 0, sizeof(x))

// ICM42688 dual device configuration
static const std::string IMU_BUFF_ENABLE_ON = "1";
static const std::string IMU_BUFF_ENABLE_OFF = "0";

// Gyroscope device (device1) configuration
static const std::string GYRO_BUFF_ENABLE_FILE = "/sys/bus/iio/devices/iio:device1/buffer/enable";
static const std::string GYRO_SAMPLING_FREQ_VALUE = "200";       // 200 hz
static const std::string GYRO_SAMPLING_FREQ_FILE = "/sys/bus/iio/devices/iio:device1/sampling_frequency";
static const std::string GYRO_SCALE_VALUE = "0.001065264";       // ICM42688 gyro scale
static const std::string GYRO_SCALE_FILE = "/sys/bus/iio/devices/iio:device1/in_anglvel_scale";

// Accelerometer device (device2) configuration
static const std::string ACCEL_BUFF_ENABLE_FILE = "/sys/bus/iio/devices/iio:device2/buffer/enable";
static const std::string ACCEL_SAMPLING_FREQ_VALUE = "200";     // 200 hz
static const std::string ACCEL_SAMPLING_FREQ_FILE = "/sys/bus/iio/devices/iio:device2/sampling_frequency";
static const std::string ACCEL_SCALE_VALUE = "0.001197101";     // ICM42688 accel scale
static const std::string ACCEL_SCALE_FILE = "/sys/bus/iio/devices/iio:device2/in_accel_scale";

// Legacy single device configuration (for backward compatibility)
static const std::string IMU_BUFF_ENABLE_FILE = "/sys/bus/iio/devices/iio:device1/buffer/enable";
static const std::string IMU_SAMPLING_FREQ_VALUE = "200";       // 200 hz
static const std::string IMU_SAMPLING_FREQ_FILE = "/sys/bus/iio/devices/iio:device1/sampling_frequency";
static const std::string IMU_ACC_SCALE_VALUE = "0.001196";       // 4g
static const std::string IMU_ACC_SCALE_FILE = "/sys/bus/iio/devices/iio:device1/in_accel_scale";
static const std::string IMU_GYRO_SCALE_VALUE = "0.001064724";       // 4g
static const std::string IMU_GYRO_SCALE_FILE = "/sys/bus/iio/devices/iio:device1/in_anglvel_scale";

static const std::string IMU_SCAN_ELEMENT_EN = "1";       //
static const std::string IMU_SCAN_ELEMENT_DIS = "0";       //

// Gyroscope device (device1) scan elements
static const std::string GYRO_SCAN_ELEMENT_X_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_x_en";
static const std::string GYRO_SCAN_ELEMENT_Y_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_y_en";
static const std::string GYRO_SCAN_ELEMENT_Z_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_z_en";
static const std::string GYRO_SCAN_ELEMENT_TIMESTAMP_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_timestamp_en";
static const std::string GYRO_TIMESTAMP_CLOCK_FILE = "/sys/bus/iio/devices/iio:device1/current_timestamp_clock";
static const std::string GYRO_BUFFER_LENGTH_FILE = "/sys/bus/iio/devices/iio:device1/buffer/length";

// Accelerometer device (device2) scan elements
static const std::string ACCEL_SCAN_ELEMENT_X_FILE = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_x_en";
static const std::string ACCEL_SCAN_ELEMENT_Y_FILE = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_y_en";
static const std::string ACCEL_SCAN_ELEMENT_Z_FILE = "/sys/bus/iio/devices/iio:device2/scan_elements/in_accel_z_en";
static const std::string ACCEL_SCAN_ELEMENT_TIMESTAMP_FILE = "/sys/bus/iio/devices/iio:device2/scan_elements/in_timestamp_en";
static const std::string ACCEL_TIMESTAMP_CLOCK_FILE = "/sys/bus/iio/devices/iio:device2/current_timestamp_clock";
static const std::string ACCEL_BUFFER_LENGTH_FILE = "/sys/bus/iio/devices/iio:device2/buffer/length";

// Legacy single device scan elements (for backward compatibility)
static const std::string IMU_SCAN_ELEMENT_ACC_X_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_accel_x_en";
static const std::string IMU_SCAN_ELEMENT_ACC_Y_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_accel_y_en";
static const std::string IMU_SCAN_ELEMENT_ACC_Z_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_accel_z_en";
static const std::string IMU_SCAN_ELEMENT_GYRO_X_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_x_en";
static const std::string IMU_SCAN_ELEMENT_GYRO_Y_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_y_en";
static const std::string IMU_SCAN_ELEMENT_GYRO_Z_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_anglvel_z_en";
static const std::string IMU_SCAN_ELEMENT_TIMESTAMP_FILE = "/sys/bus/iio/devices/iio:device1/scan_elements/in_timestamp_en";

static const std::string IMU_TIMESTAMP_CLOCK_MONOTIC = "monotonic";       //
static const std::string IMU_TIMESTAMP_CLOCK_REALTIME = "realtime";       //
static const std::string IMU_TIMESTAMP_CLOCK_FILE = "/sys/bus/iio/devices/iio:device1/current_timestamp_clock";

static const std::string IMU_BUFFER_LENGHT_VALUE = "480";       //
static const std::string IMU_BUFFER_LENGHT_FILE = "/sys/bus/iio/devices/iio:device1/buffer/length";





static  auroravision_imustream_desc_t  imustream_profiles [] = {
    // profile id 0, default profile
    {
        .version = 0,
        .profile_id = 0,
        .correlated_stream_id = 1,

        .samplerate = IMU_SENSOR_OUTPUT_RATE,
        .feature_flag = 0,
        .sensitivity_gyro_dps = IMU_SENSOR_GYRO_SENSITIVITY,
        .sensitivity_acc_g = IMU_SENSOR_ACC_SENSITIVITY,
    },
};


static uint8_t data_buf[480] = {0};



ImuSensor::ImuSensor(rp::ev::EventDispatcher<AuroraVisionEvent>& dispatcher, std::string gyro_dev, std::string accel_dev)
    : gyro_dev_(gyro_dev)
    , accel_dev_(accel_dev)
    , gyro_dev_fd_(-1)
    , accel_dev_fd_(-1)
    , last_gyro_timestamp_(0)
    , last_accel_timestamp_(0)
    , dev_(gyro_dev)  // For backward compatibility
    , dev_fd_(-1)
    , last_frame_timestamp_(0)
    , eventDispatcher_(dispatcher)
    , opLocker_(true)
    , evtdispLocker_(true)
    , eventIDCounter_(0)
    , eventDisptchStarted_(false)
    , sensroFrameStarted_(false)
    , isSensorOpened_(false)
    , default_profile_(0)
    , active_profile_(0)
    // , enabled(false)
    // , status(AURORAVISION_STREAM_STATUS_IDLE)
    // , errorCode(AURORAVISION_STREAM_ERRORCODE_NONE)
{

}


ImuSensor::~ImuSensor()
{
    close();
}

uint64_t ImuSensor::findClosestTimestamp(uint64_t target_timestamp, const std::deque<std::pair<uint64_t, geometry_msgs::msg::Vector3>>& data_queue)
{
    if (data_queue.empty()) {
        return 0;
    }

    uint64_t closest_timestamp = 0;
    uint64_t min_diff = UINT64_MAX;
    const uint64_t timestamp_sync_tolerance_ns = 10000000; // 10ms tolerance

    for (const auto& data_pair : data_queue) {
        uint64_t timestamp = data_pair.first;
        uint64_t diff = (timestamp > target_timestamp) ? (timestamp - target_timestamp) : (target_timestamp - timestamp);

        if (diff < min_diff) {
            min_diff = diff;
            closest_timestamp = timestamp;
        }
    }

    // Only return the timestamp if it's within tolerance
    if (min_diff <= timestamp_sync_tolerance_ns) {
        return closest_timestamp;
    }

    return 0;
}


u_result ImuSensor::open()
{
    rp::hal::AutoLocker l(opLocker_);

#if 1
    // Check if using ICM42688 dual device setup or legacy single device
    bool use_dual_devices = (gyro_dev_ == IMU_DEFAULT_GYRO_DEVICE_FILE && accel_dev_ == IMU_DEFAULT_ACCEL_DEVICE_FILE);

    if (!use_dual_devices && dev_ != IMU_DEFAULT_DEVICE_FILE)
    {
        fprintf(stderr, "error : not support device %s , supported: %s or dual devices %s + %s \n",
                dev_.c_str(), IMU_DEFAULT_DEVICE_FILE.c_str(),
                IMU_DEFAULT_GYRO_DEVICE_FILE.c_str(), IMU_DEFAULT_ACCEL_DEVICE_FILE.c_str());
        return RESULT_OPERATION_NOT_SUPPORT;
    }

    int fd = -1;
    int err = -1;

    if (use_dual_devices) {
        // Configure gyroscope device (device1) for ICM42688
        //disable gyro buffer enable first
        do
        {
            fd = ::open(GYRO_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);

        //disable accel buffer enable first
        do
        {
            fd = ::open(ACCEL_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    } else {
        // Legacy single device configuration
        //disable buffer enable first
        do
        {
            fd = ::open(IMU_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    }

    if (use_dual_devices) {
        // Set gyroscope sampling frequency
        do
        {
            fd = ::open(GYRO_SAMPLING_FREQ_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", GYRO_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, GYRO_SAMPLING_FREQ_VALUE.c_str(), GYRO_SAMPLING_FREQ_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", GYRO_SAMPLING_FREQ_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", GYRO_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);

        // Set accelerometer sampling frequency
        do
        {
            fd = ::open(ACCEL_SAMPLING_FREQ_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", ACCEL_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, ACCEL_SAMPLING_FREQ_VALUE.c_str(), ACCEL_SAMPLING_FREQ_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", ACCEL_SAMPLING_FREQ_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", ACCEL_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    } else {
        // Legacy single device sampling frequency
        //set sampling frequency
        do
        {
            fd = ::open(IMU_SAMPLING_FREQ_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", IMU_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_SAMPLING_FREQ_VALUE.c_str(), IMU_SAMPLING_FREQ_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_SAMPLING_FREQ_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", IMU_SAMPLING_FREQ_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    }

    if (use_dual_devices) {
        // Set accelerometer scale for ICM42688
        do
        {
            fd = ::open(ACCEL_SCALE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", ACCEL_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, ACCEL_SCALE_VALUE.c_str(), ACCEL_SCALE_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", ACCEL_SCALE_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", ACCEL_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);

        // Set gyroscope scale for ICM42688
        do
        {
            fd = ::open(GYRO_SCALE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", GYRO_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, GYRO_SCALE_VALUE.c_str(), GYRO_SCALE_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", GYRO_SCALE_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", GYRO_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    } else {
        // Legacy single device scale configuration
        //set acc scale
        do
        {
            fd = ::open(IMU_ACC_SCALE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", IMU_ACC_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_ACC_SCALE_VALUE.c_str(), IMU_ACC_SCALE_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_ACC_SCALE_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", IMU_ACC_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);

        //set gyro scale
        do
        {
            fd = ::open(IMU_GYRO_SCALE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", IMU_GYRO_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::write(fd, IMU_GYRO_SCALE_VALUE.c_str(), IMU_GYRO_SCALE_VALUE.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_GYRO_SCALE_VALUE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", IMU_GYRO_SCALE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    }

    //set scan acc x element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_ACC_X_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_ACC_X_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_ACC_X_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan acc y element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_ACC_Y_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_ACC_Y_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_ACC_Y_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan acc z element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_ACC_Z_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_ACC_Z_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_ACC_Z_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan gyro x element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_GYRO_X_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_GYRO_X_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_GYRO_X_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan gyro y element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_GYRO_Y_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_GYRO_Y_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_GYRO_Y_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan gyro z element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_GYRO_Z_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_GYRO_Z_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_GYRO_Z_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set scan timestamp element
    do
    {
        fd = ::open(IMU_SCAN_ELEMENT_TIMESTAMP_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_SCAN_ELEMENT_TIMESTAMP_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_SCAN_ELEMENT_EN.c_str(), IMU_SCAN_ELEMENT_EN.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_SCAN_ELEMENT_EN.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_SCAN_ELEMENT_TIMESTAMP_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set timestamp clock
    do
    {
        fd = ::open(IMU_TIMESTAMP_CLOCK_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_TIMESTAMP_CLOCK_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_TIMESTAMP_CLOCK_MONOTIC.c_str(), IMU_TIMESTAMP_CLOCK_MONOTIC.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_TIMESTAMP_CLOCK_MONOTIC.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_TIMESTAMP_CLOCK_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);

    //set buffer length
    do
    {
        fd = ::open(IMU_BUFFER_LENGHT_FILE.c_str(), O_WRONLY);
        if (fd < 0)
        {
            fprintf(stderr, "error : open %s \n", IMU_BUFFER_LENGHT_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::write(fd, IMU_BUFFER_LENGHT_VALUE.c_str(), IMU_BUFFER_LENGHT_VALUE.size()+1);
        if (err < 0)
        {
            fprintf(stderr, "error : write %s \n", IMU_BUFFER_LENGHT_VALUE.c_str());
            return RESULT_OPERATION_FAIL;
        }

        err = ::close(fd);
        if (err < 0)
        {
            fprintf(stderr, "error : close %s \n", IMU_BUFFER_LENGHT_FILE.c_str());
            return RESULT_OPERATION_FAIL;
        }
    } while (0);
#endif

    if (use_dual_devices) {
        // Open gyroscope device
        gyro_dev_fd_ = ::open(gyro_dev_.c_str(), O_RDONLY);
        if (gyro_dev_fd_ < 0)
        {
            fprintf(stderr, "error : open gyro device %s \n", gyro_dev_.c_str());
            return RESULT_OPERATION_FAIL;
        }

        // Open accelerometer device
        accel_dev_fd_ = ::open(accel_dev_.c_str(), O_RDONLY);
        if (accel_dev_fd_ < 0)
        {
            fprintf(stderr, "error : open accel device %s \n", accel_dev_.c_str());
            ::close(gyro_dev_fd_);
            gyro_dev_fd_ = -1;
            return RESULT_OPERATION_FAIL;
        }

        fprintf(stderr, "info : open gyro device %s ok, fd = %d\n", gyro_dev_.c_str(), gyro_dev_fd_);
        fprintf(stderr, "info : open accel device %s ok, fd = %d\n", accel_dev_.c_str(), accel_dev_fd_);

        // Set legacy fd for backward compatibility
        dev_fd_ = gyro_dev_fd_;
    } else {
        // Legacy single device opening
        dev_fd_ = ::open(dev_.c_str(), O_RDONLY);
        if (dev_fd_ < 0)
        {
            fprintf(stderr, "error : open %s \n", dev_.c_str());
            return RESULT_OPERATION_FAIL;
        }

        fprintf(stderr, "info : open %s ok, fd = %d\n", dev_.c_str(), dev_fd_);
    }

    isSensorOpened_ = true;

    return RESULT_OK;
}


u_result ImuSensor::setActiveStreamProfile(size_t id)
{
    rp::hal::AutoLocker l(opLocker_);

    if (id >= _countof(imustream_profiles))
    {
        fprintf(stderr, "Error: id >= _countof(imustream_profiles)\n");
        return RESULT_OPERATION_FAIL;
    }
    
    active_profile_ = id;
    // enabled = true;

    resetDispatchEventBuffer_();
    return RESULT_OK;
}

// u_result ImuSensor::getStreamStatus(bool & en, _u32 & st, _u32 & err)
// {
//     // en = enabled;
//     // st = status;
//     // err = errorCode;

//     return RESULT_OK;
// }


u_result ImuSensor::close()
{
    rp::hal::AutoLocker l(opLocker_);

    if (!sensroFrameStarted_ && !eventDisptchStarted_) return RESULT_ALREADY_DONE;

    if (sensroFrameStarted_)
    {
        sensroFrameStarted_ = false;
        sensroFrameThread_.join();
    }

    if (isSensorOpened_ )
    {
        // Check if using dual devices
        bool use_dual_devices = (gyro_dev_fd_ >= 0 && accel_dev_fd_ >= 0);

        if (use_dual_devices) {
            // Close dual devices
            if (gyro_dev_fd_ >= 0)
            {
                int err = ::close(gyro_dev_fd_);
                if (err < 0)
                {
                    fprintf(stderr, "error : close gyro device %s \n", gyro_dev_.c_str());
                }
                gyro_dev_fd_ = -1;
            }

            if (accel_dev_fd_ >= 0)
            {
                int err = ::close(accel_dev_fd_);
                if (err < 0)
                {
                    fprintf(stderr, "error : close accel device %s \n", accel_dev_.c_str());
                }
                accel_dev_fd_ = -1;
            }

            // Disable gyro buffer
            do
            {
                int fd = ::open(GYRO_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
                if (fd < 0)
                {
                    fprintf(stderr, "error : open %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                    break;
                }

                int err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
                if (err < 0)
                {
                    fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                    break;
                }

                err = ::close(fd);
                if (err < 0)
                {
                    fprintf(stderr, "error : close %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                    break;
                }
            } while (0);

            // Disable accel buffer
            do
            {
                int fd = ::open(ACCEL_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
                if (fd < 0)
                {
                    fprintf(stderr, "error : open %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                    break;
                }

                int err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
                if (err < 0)
                {
                    fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                    break;
                }

                err = ::close(fd);
                if (err < 0)
                {
                    fprintf(stderr, "error : close %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                    break;
                }
            } while (0);
        } else {
            // Legacy single device close
            if (dev_fd_ > 0)
            {
                int err = ::close(dev_fd_);
                if (err < 0)
                {
                    fprintf(stderr, "error : close %s \n", dev_.c_str());
                    //return RESULT_OPERATION_FAIL;
                }
            }

            //disable buffer enable
            do
            {
                int fd = ::open(IMU_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
                if (fd < 0)
                {
                    fprintf(stderr, "error : open %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                    break;
                    //return RESULT_OPERATION_FAIL;
                }

                int err = ::write(fd, IMU_BUFF_ENABLE_OFF.c_str(), IMU_BUFF_ENABLE_OFF.size()+1);
                if (err < 0)
                {
                    fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_OFF.c_str());
                    break;
                    //return RESULT_OPERATION_FAIL;
                }

                err = ::close(fd);
                if (err < 0)
                {
                    fprintf(stderr, "error : close %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                    break;
                    //return RESULT_OPERATION_FAIL;
                }
            } while (0);
        }

        isSensorOpened_ = false;
    }
        
    if (eventDisptchStarted_)
    {
        eventDisptchStarted_ = false;
        pendingEvent_.set();
        eventDispatchThread_.join();
    }

    std::list<AuroraVisionEvent> emptyQueue;
    pendingEventQueue_.swap(emptyQueue);


    // enabled = false;
    // status = AURORAVISION_STREAM_STATUS_IDLE;
    // errorCode = AURORAVISION_STREAM_ERRORCODE_NONE;

    return RESULT_OK;
}

u_result ImuSensor::streamOn()
{
    rp::hal::AutoLocker l(opLocker_);

    resetDispatchEventBuffer_();
    resetFrameEventBuffer_();

    if (!isSensorOpened_)
    {
        return RESULT_OPERATION_FAIL;
    }
    
    // Check if using dual devices
    bool use_dual_devices = (gyro_dev_fd_ >= 0 && accel_dev_fd_ >= 0);

    if (use_dual_devices) {
        // Enable gyro buffer
        do
        {
            int fd = ::open(GYRO_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            int err = ::write(fd, IMU_BUFF_ENABLE_ON.c_str(), IMU_BUFF_ENABLE_ON.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_ON.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", GYRO_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);

        // Enable accel buffer
        do
        {
            int fd = ::open(ACCEL_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }

            int err = ::write(fd, IMU_BUFF_ENABLE_ON.c_str(), IMU_BUFF_ENABLE_ON.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_ON.c_str());
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", ACCEL_BUFF_ENABLE_FILE.c_str());
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    } else {
        // Legacy single device buffer enable
        //enable buffer enable
        do
        {
            int fd = ::open(IMU_BUFF_ENABLE_FILE.c_str(), O_WRONLY);
            if (fd < 0)
            {
                fprintf(stderr, "error : open %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                //break;
                return RESULT_OPERATION_FAIL;
            }

            int err = ::write(fd, IMU_BUFF_ENABLE_ON.c_str(), IMU_BUFF_ENABLE_ON.size()+1);
            if (err < 0)
            {
                fprintf(stderr, "error : write %s \n", IMU_BUFF_ENABLE_ON.c_str());
                //break;
                return RESULT_OPERATION_FAIL;
            }

            err = ::close(fd);
            if (err < 0)
            {
                fprintf(stderr, "error : close %s \n", IMU_BUFF_ENABLE_FILE.c_str());
                //break;
                return RESULT_OPERATION_FAIL;
            }
        } while (0);
    }
    
    sensroFrameStarted_ = true;
    sensroFrameThread_ = CLASS_THREAD(ImuSensor, frameFetchProc_);

#if 0
    // start event dispatch thread ...
    eventDisptchStarted_ = true;
    eventDispatchThread_ = CLASS_THREAD(ImuSensor, eventDispProc_);
#endif

    // status = AURORAVISION_STREAM_STATUS_WORKING;
    fprintf(stderr, "info : ImuSensor::streamOn \n");
    return RESULT_OK;
}

u_result ImuSensor::eventDispProc_()
{
    fprintf(stderr, "start ImuSensor::eventDispProc_()\n");
    while (eventDisptchStarted_)
    {
        AuroraVisionEvent currentEvt;
        evtdispLocker_.lock();
        if (pendingEventQueue_.empty()) {
            evtdispLocker_.unlock();
            pendingEvent_.wait();
            continue;
        }

        currentEvt = pendingEventQueue_.front();
        pendingEventQueue_.pop_front();
        evtdispLocker_.unlock();

#if 0
        // static uint64_t last_frame = 0;
        static uint64_t frame_count = 0;

        static uint64_t last_ts_us = 0;
        
        frame_count++;

        uint64_t current_ts_us = currentEvt.eventData->device_timestamp/1000;
        if (current_ts_us - last_ts_us >= 2000000)
        {
                printf("IMU fps: %4.1f\n", frame_count * 1000000.0f / (current_ts_us - last_ts_us));
                frame_count = 0;
                last_ts_us = current_ts_us;
        }

#endif
        if(default_profile_ != active_profile_)
        {
            //TODO: not support yet
        }
        else
        {
            eventDispatcher_.dispatchMessage(AuroraVisionDrv::AURORAVISONDRV_SENSOR_EVENT, currentEvt);
            // printf("IMU fps: \n");
        }
    }
    fprintf(stderr, "end ImuSensor::eventDispProc_()\n");
    return RESULT_OK;

}


u_result ImuSensor::publishSensorEvent(AuroraVisionEvent& evt)
{
    if (!eventDisptchStarted_) return RESULT_OPERATION_FAIL;
    
    if (!evt) return RESULT_OPERATION_FAIL;
    evt.eventData->eventID = eventIDCounter_;
    ++eventIDCounter_;
    
    rp::hal::AutoLocker l(evtdispLocker_);
    pendingEventQueue_.push_back(evt);
    pendingEvent_.set();
    return RESULT_OK;
}



// fetch sensor frame thread
u_result ImuSensor::frameFetchProc_()
{
    // Check if using dual devices
    bool use_dual_devices = (gyro_dev_fd_ >= 0 && accel_dev_fd_ >= 0);

    if (use_dual_devices) {
        fprintf(stderr, "start ImuSensor::frameFetchProc_() dual devices: gyro_fd=%d, accel_fd=%d\n", gyro_dev_fd_, accel_dev_fd_);
    } else {
        fprintf(stderr, "start ImuSensor::frameFetchProc_() single device: fd=%d\n", dev_fd_);
    }

    rp::hal::Thread::SetSelfPriority(rp::hal::Thread::PRIORITY_REALTIME);

    if (use_dual_devices) {
        // Setup polling for dual devices
        struct pollfd fds[2];
        fds[0].fd = gyro_dev_fd_;
        fds[0].events = POLLIN;
        fds[0].revents = 0;

        fds[1].fd = accel_dev_fd_;
        fds[1].events = POLLIN;
        fds[1].revents = 0;


        // Dual device processing with accelerometer-based timestamp synchronization
        while (sensroFrameStarted_)
        {
            int ret = poll(fds, 2, POLL_TIMEOUT);
            if (ret < 0)
            {
                fprintf(stderr, "Error: poll error\n");
                continue;
            }
            else if (ret == 0)
            {
                fprintf(stderr, "Error: poll timeout\n");
                continue;
            }

            // Check gyroscope device
            if (fds[0].revents & POLLIN) {
                ret = ::read(fds[0].fd, &data_buf, sizeof(data_buf));
                if (ret >= static_cast<int>(sizeof(gyro_device_buffer_frame))) {
                    int num_frames = ret / sizeof(gyro_device_buffer_frame);

                    for (int i = 0; i < num_frames; ++i) {
                        gyro_device_buffer_frame* frame = reinterpret_cast<gyro_device_buffer_frame*>(
                            data_buf + i * sizeof(gyro_device_buffer_frame));

                        // Extract timestamp
                        uint64_t timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

                        // Skip if same timestamp as last frame
                        if (timestamp == last_gyro_timestamp_) {
                            continue;
                        }
                        last_gyro_timestamp_ = timestamp;

                        // Extract gyroscope data
                        int16_t gyro_x = (frame->gyro_x_h << 8) | frame->gyro_x_l;
                        int16_t gyro_y = (frame->gyro_y_h << 8) | frame->gyro_y_l;
                        int16_t gyro_z = (frame->gyro_z_h << 8) | frame->gyro_z_l;

                        // Convert to proper units
                        geometry_msgs::msg::Vector3 gyro_data;
                        gyro_data.x = static_cast<double>(gyro_x) * GYRO_Q16_FACTOR / 65536.0;
                        gyro_data.y = static_cast<double>(gyro_y) * GYRO_Q16_FACTOR / 65536.0;
                        gyro_data.z = static_cast<double>(gyro_z) * GYRO_Q16_FACTOR / 65536.0;

                        // Store in gyro queue
                        gyro_data_queue_.push_back(std::make_pair(timestamp, gyro_data));

                        // Keep queue size manageable
                        if (gyro_data_queue_.size() > 100) {
                            gyro_data_queue_.pop_front();
                        }
                    }
                }
            }

            // Check accelerometer device
            if (fds[1].revents & POLLIN) {
                ret = ::read(fds[1].fd, &data_buf, sizeof(data_buf));
                if (ret >= static_cast<int>(sizeof(accel_device_buffer_frame))) {
                    int num_frames = ret / sizeof(accel_device_buffer_frame);

                    for (int i = 0; i < num_frames; ++i) {
                        accel_device_buffer_frame* frame = reinterpret_cast<accel_device_buffer_frame*>(
                            data_buf + i * sizeof(accel_device_buffer_frame));

                        // Extract timestamp (use accelerometer as base timestamp)
                        uint64_t accel_timestamp = *reinterpret_cast<uint64_t*>(frame->timestamp);

                        // Skip if same timestamp as last frame
                        if (accel_timestamp == last_accel_timestamp_) {
                            continue;
                        }
                        last_accel_timestamp_ = accel_timestamp;

                        // Extract accelerometer data
                        int16_t acc_x = (frame->acc_x_h << 8) | frame->acc_x_l;
                        int16_t acc_y = (frame->acc_y_h << 8) | frame->acc_y_l;
                        int16_t acc_z = (frame->acc_z_h << 8) | frame->acc_z_l;

                        // Convert to proper units
                        geometry_msgs::msg::Vector3 accel_data;
                        accel_data.x = static_cast<double>(acc_x) * ACC_Q16_FACTOR / 65536.0;
                        accel_data.y = static_cast<double>(acc_y) * ACC_Q16_FACTOR / 65536.0;
                        accel_data.z = static_cast<double>(acc_z) * ACC_Q16_FACTOR / 65536.0;

                        // Store in accel queue
                        accel_data_queue_.push_back(std::make_pair(accel_timestamp, accel_data));

                        // Keep queue size manageable
                        if (accel_data_queue_.size() > 100) {
                            accel_data_queue_.pop_front();
                        }

                        // Find closest gyro timestamp for this accelerometer timestamp
                        uint64_t closest_gyro_timestamp = findClosestTimestamp(accel_timestamp, gyro_data_queue_);

                        if (closest_gyro_timestamp != 0) {
                            // Find the gyro data with the closest timestamp
                            geometry_msgs::msg::Vector3 matched_gyro_data;
                            for (const auto& gyro_pair : gyro_data_queue_) {
                                if (gyro_pair.first == closest_gyro_timestamp) {
                                    matched_gyro_data = gyro_pair.second;
                                    break;
                                }
                            }

                            // Publish synchronized IMU data using accelerometer timestamp as base
                            auto frame_evt_data = frameEvent_.getDataAs<IMU6oFData>();
                            memset(&frame_evt_data->imu, 0, sizeof(auroravision_streamevt_data_imu_6dof));

                            // Set accelerometer data
                            frame_evt_data->imu.acc_q16[0] = static_cast<int32_t>(accel_data.x * 65536.0);
                            frame_evt_data->imu.acc_q16[1] = static_cast<int32_t>(accel_data.y * 65536.0);
                            frame_evt_data->imu.acc_q16[2] = static_cast<int32_t>(accel_data.z * 65536.0);

                            // Set gyroscope data
                            frame_evt_data->imu.gyro_q16[0] = static_cast<int32_t>(matched_gyro_data.x * 65536.0);
                            frame_evt_data->imu.gyro_q16[1] = static_cast<int32_t>(matched_gyro_data.y * 65536.0);
                            frame_evt_data->imu.gyro_q16[2] = static_cast<int32_t>(matched_gyro_data.z * 65536.0);

                            // Use accelerometer timestamp as the base timestamp
                            frame_evt_data->device_timestamp = accel_timestamp;
                            frame_evt_data->timestamp_ns = accel_timestamp;

                            if (last_frame_timestamp_ != accel_timestamp) {
                                last_frame_timestamp_ = accel_timestamp;
                                eventDispatcher_.dispatchMessage(AuroraVisionDrv::AURORAVISONDRV_SENSOR_EVENT, frameEvent_);
                            }
                        }
                    }
                }
            }
        }
    } else {
        // Legacy single device processing
        struct pollfd fds;
        fds.fd = dev_fd_;
        fds.events = POLLIN;
        fds.revents = 0;

        while (sensroFrameStarted_)
        {
            int ret = poll(&fds, 1, POLL_TIMEOUT);
            if (ret < 0)
            {
                fprintf(stderr, "Error: poll error\n");
                continue;
            }
            else if (ret == 0)
            {
                fprintf(stderr, "Error: poll timeout\n");
                continue;
            }

            if (fds.revents & POLLIN)
            {
                ret=::read(fds.fd, &data_buf, sizeof(data_buf));

                if ((ret < 0) || (ret < sizeof(imu_device_buffer_frame)))
                {
                    fprintf(stderr, "Error: read error %d \n", ret);
                    continue;
                }

                imu_device_buffer_frame *frame_buf = (imu_device_buffer_frame *)data_buf;

                auto frame_evt_data = frameEvent_.getDataAs<IMU6oFData>();
                for (int i = 0; i < ret/sizeof(imu_device_buffer_frame); ++i)
                {
                    memset(&frame_evt_data->imu, 0, sizeof(auroravision_streamevt_data_imu_6dof));

                    frame_evt_data->imu.acc_q16[0] = ((int16_t)(frame_buf[i].acc_x_h << 8 | frame_buf[i].acc_x_l)) * ACC_Q16_FACTOR;
                    frame_evt_data->imu.acc_q16[1] = ((int16_t)(frame_buf[i].acc_y_h << 8 | frame_buf[i].acc_y_l)) * ACC_Q16_FACTOR;
                    frame_evt_data->imu.acc_q16[2] = ((int16_t)(frame_buf[i].acc_z_h << 8 | frame_buf[i].acc_z_l)) * ACC_Q16_FACTOR;
                    frame_evt_data->imu.gyro_q16[0] = ((int16_t)(frame_buf[i].gyro_x_h << 8 | frame_buf[i].gyro_x_l)) * GYRO_Q16_FACTOR;
                    frame_evt_data->imu.gyro_q16[1] = ((int16_t)(frame_buf[i].gyro_y_h << 8 | frame_buf[i].gyro_y_l)) * GYRO_Q16_FACTOR;
                    frame_evt_data->imu.gyro_q16[2] = ((int16_t)(frame_buf[i].gyro_z_h << 8 | frame_buf[i].gyro_z_l)) * GYRO_Q16_FACTOR;

                    last_frame_timestamp_ = frame_evt_data->device_timestamp;
                    frame_evt_data->device_timestamp = *(uint64_t *)frame_buf[i].timestamp;
                    frame_evt_data->timestamp_ns = frame_evt_data->device_timestamp;

                    if (last_frame_timestamp_ != frame_evt_data->device_timestamp)
                    {
                        eventDispatcher_.dispatchMessage(AuroraVisionDrv::AURORAVISONDRV_SENSOR_EVENT, frameEvent_);
                    }
                    else
                    {
                        fprintf(stderr, "warning: same timestamp %ld \n", frame_evt_data->device_timestamp);
                    }
                }
            }
            else
            {
                fprintf(stderr, "Error: poll event error\n");
                continue;
            }
        }
    }


    fprintf(stderr, "end ImuSensor::frameFetchProc_()\n");
    return RESULT_OK;
}


void ImuSensor::resetDispatchEventBuffer_()
{
    //dispatch event use
    auroravision_streamevt_data_imu_6dof imu_data;
    memset(&imu_data, 0, sizeof(auroravision_streamevt_data_imu_6dof));

    dispatchEvent_.eventData = boost::make_shared<IMU6oFData>(imu_data);
    dispatchEvent_.eventData->streamID = imustream_profiles[active_profile_].correlated_stream_id;
    dispatchEvent_.eventData->eventType = AURORAVISION_EVT_IMU_6DOF;

}


void ImuSensor::resetFrameEventBuffer_()
{
    //frame event use
    auroravision_streamevt_data_imu_6dof imu_data;
    memset(&imu_data, 0, sizeof(auroravision_streamevt_data_imu_6dof));

    frameEvent_.eventData = boost::make_shared<IMU6oFData>(imu_data);
    frameEvent_.eventData->streamID = imustream_profiles[default_profile_].correlated_stream_id;
    frameEvent_.eventData->eventType = AURORAVISION_EVT_IMU_6DOF;
}

// size_t ImuSensor::getSteamEvtCnt() 
// { 
//     return _countof(streamevent_descriptors);
// }

size_t ImuSensor::getProfileCnt() 
{ 
    return _countof(imustream_profiles);
}

// auroravision_streamevt_desc_t * ImuSensor::getStreamDescs() 
// {
//     return (auroravision_streamevt_desc_t *)&streamevent_descriptors;
// }

auroravision_imustream_desc_t * ImuSensor::getProfiles() 
{
    return (auroravision_imustream_desc_t *)&imustream_profiles;
}

size_t ImuSensor::getProfileDescSize()
{
    return sizeof(auroravision_imustream_desc_t);
}

size_t ImuSensor::getActiveProfileId()
{
    return active_profile_;
}
size_t ImuSensor::getDefaultProfileId()
{
    return default_profile_;
}



}}}
