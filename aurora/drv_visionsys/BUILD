
load("@bazel_tools//tools/build_defs/pkg:pkg.bzl", "pkg_tar")


package(
    default_visibility = ["//visibility:public"],
)


filegroup(
    name = "common",
    srcs = glob([
        "common/**/*.h",
    ]),
)

filegroup(
    name = "inc",
    srcs = glob(["inc/**/*.h"])

)
filegroup(
    name = "srcs",
    srcs = glob(
        include = ["src/**/*.cpp"],
    )
)


cc_library(
    name = "visiondrv",
    srcs = [":srcs"],
    hdrs = [":inc"] + [":common"],
    includes = ["src", "inc", "common"],
    linkstatic = True,
    deps = [
        "//legacy/infra:infra",
        "@libv4l",
        "@opencv4"
    ],
    linkopts = [
        "-lv4l2",
        "-lv4lconvert",
        "-ljpeg",
        "-ldl",
        "-lrt",
        "-lm",
    ]+ select(
        {
            "//tools/config:x86_64" : [
                #"-littnotify",
                #"-lippiw",
                #"-lippicv",
                "-Wl,-rpath,$$ORIGIN/../../../3rdparty/precompiled/linux-x86_64-gcc9",
            ],
            "//tools/config:aarch64" : [
                "-Wl,-rpath,$$ORIGIN/../../../3rdparty/precompiled/linux-aarch64-gcc9",
            ]
        },
        no_match_error = "no matching rule for //tools/config:<gcc_version>"
    )
)

pkg_tar(
    name = "release_headers",
    srcs = [
        ":common",
        ":inc"
    ],
    package_dir = "usr/include",
    strip_prefix = "include/",
)

pkg_tar(
    name = "release_libs",
    srcs = [
        ":visiondrv"
    ],
    package_dir = "usr/lib",
)

pkg_tar(
    name = "release",
    deps = [
        ":release_headers",
        ":release_libs",
    ],
)