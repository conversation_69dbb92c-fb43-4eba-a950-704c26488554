/*
 *  Data Recording Utilty for AuroraCore Vision System
 *  Main Application Logic
 *  
 *  Copyright 2013 - 2022 SLAMTEC Co., Ltd.
 * 
 *  Initial version by CSK
 */

#include "common.h"
#include "grabberapplication.h"
#include "ui/uiprovider.h"

#include <string>
#include <vector>
#include <boost/filesystem.hpp>
#include <ctime>
#include <boost/circular_buffer.hpp>

using namespace rp::auroracore::drv;
using namespace rp::auroracore::tools;

namespace rp::auroracore::tools {

const char * GrabberApplicationParam::OPT_DEF_RECORDNAME = "camrecord";

GrabberApplicationParam::GrabberApplicationParam()
: opt_profileID(-1)
, opt_slientMode(false)
, opt_deviceID(0)
, opt_showHelp(false)
, opt_listDevices(false)
, opt_listProfiles(false)
, opt_recorderType(DataRecorder::RECORDER_TYPE_DISCRETEFILE)
, opt_recordingFPS(0)
{
}



GrabberApplication::GrabberApplication()
    : isInited_(false)
    , requireExit_(false)

{
}

GrabberApplication::~GrabberApplication()
{
    onExit();
}

static bool parseRecordMode(const char * str, int & type)
{
    static const struct {
        const char * str;
        DataRecorder::RecorderType type;
    }  modemapper[] = {
        {"discrete", DataRecorder::RECORDER_TYPE_DISCRETEFILE},
        {"ondemand", DataRecorder::RECORDER_TYPE_ONDEMAND},
    };

    
    for (size_t pos = 0; pos < _countof(modemapper); ++pos)
    {
        if (strcmp(modemapper[pos].str, str) == 0) {
            type = modemapper[pos].type;
            return true;
        }
    }
    return false;
}

bool GrabberApplication::parseCommandLine(int argc, const char* argv[])
{
    // parse the command line...
    {
        for (int i = 1; i < argc; ++i)
        {
            if ((strcmp(argv[i], "-p") == 0) || (strcmp(argv[i], "--profile") == 0))
            {
                if (++i >= argc) {
                    fprintf(stderr, "Error, missing argument for profile id.\n");
                    param_.opt_showHelp = true;
                    break;
                }

                param_.opt_profileID = strtol(argv[i], nullptr, 0);
            }
            else if ((strcmp(argv[i], "-s") == 0) || (strcmp(argv[i], "--slient") == 0)) {
                param_.opt_slientMode = true;
            }
            else if ((strcmp(argv[i], "-l") == 0) || (strcmp(argv[i], "--list") == 0)) {
                param_.opt_listDevices = true;
            }
            else if (strcmp(argv[i], "-d") == 0) {
                if (++i >= argc) {
                    fprintf(stderr, "Error, missing argument for device id.\n");
                    param_.opt_showHelp = true;
                    break;
                }

                param_.opt_deviceID = strtol(argv[i], nullptr, 0);
            }
            else if ((strcmp(argv[i], "-h") == 0) || (strcmp(argv[i], "--help") == 0)) {
                param_.opt_showHelp = true;
            }
            else if (strcmp(argv[i], "--list-profiles") == 0) {
                param_.opt_listProfiles = true;
            }
            else if ((strcmp(argv[i], "-m") == 0) || (strcmp(argv[i], "--mode") == 0)) {
                if (++i >= argc) {
                    fprintf(stderr, "Error, missing argument for mode string.\n");
                    param_.opt_showHelp = true;
                    break;
                }

                if (!parseRecordMode(argv[i], param_.opt_recorderType)) {
                    fprintf(stderr, "Error, invalid record mode %s.\n", argv[i]);
                    param_.opt_showHelp = true;
                    break;
                }
            }
            else if ((strcmp(argv[i], "-D") == 0) || (strcmp(argv[i], "--folder") == 0)) {
                if (++i >= argc) {
                    fprintf(stderr, "Error, missing argument for record folder.\n");
                    param_.opt_showHelp = true;
                    break;
                }

                param_.opt_recordFolder = argv[i];
            }
            else if ((strcmp(argv[i], "-f") == 0) || (strcmp(argv[i], "--fps") == 0)) {
                if (++i >= argc) {
                    fprintf(stderr, "Error, missing argument for the fps option.\n");
                    param_.opt_showHelp = true;
                    break;
                }

                param_.opt_recordingFPS = strtol(argv[1], NULL, 0);
            }
            else {
                param_.opt_recordName = argv[i];
            }

        }

        if (param_.opt_recordName.length() == 0) {
            char currentTimeString[100];
            char recordfilename_buffer[255];
            std::time_t t = std::time(nullptr);
            std::tm* currentTime = std::localtime(&t);
            strftime(currentTimeString, sizeof(currentTimeString), "%Y-%m-%d_%H-%M-%S", currentTime);

            snprintf(recordfilename_buffer, sizeof(recordfilename_buffer), "%s_%s", param_. OPT_DEF_RECORDNAME, currentTimeString);
            printf("Warning, no record name has been specified, using the default name : %s.\n", recordfilename_buffer);
            param_.opt_recordName = recordfilename_buffer;

            printf("INFO: grabbed data will be stored to the folder %s.\n", param_.opt_recordName.c_str());
        }
    }
	return true;
}

void GrabberApplication::showHelp(int argc, const char* argv[])
{
    printf("Data recoder for AuroraCore Vision System\n\n"
        "Usage:\n"
        "%s [options] <record name>\n\n"

        "Options:\n"
        "-p,--profile           Specify the profile to be used by its id number.\n"
        "                       By default, the highest allowed profile will be used.n\n"
        "-d                     Specify which camera device to be used.\n"
        "                       The first available device will be used by default.\n"
        "-m,--mode <mode>       Specify the recorder mode.\n"
        "                       Options are:\n"
        "                           discrete (default)\n"
        "                           ondemand\n"
        "-f,--fps               Specify the recording fps.\n"
        "-D,--folder            Specify the folder to place the record data.\n"
        "                       Current folder will be used if not be specified.\n"
        "-s,--slient            Do not show gui window.\n"
        "-h,--help              Show this help message.\n"
        "-l,--list              List all availbe devices and exit.\n"
        "--list-profiles        List all profile configure of the specified device and exit.\n"
        
        , argv[0]
    );
}

bool GrabberApplication::init()
{
    u_result ans;

    if (isInited_) return true;

    ui::UIProvider::ProviderType uiType = ui::UIProvider::UIPROVIDER_TYPE_DEFAULT;

    if (param_.opt_slientMode) uiType = ui::UIProvider::UIPROVIDER_TYPE_HEADLESS;
    ui_ = boost::shared_ptr<ui::UIProvider>(ui::UIProvider::CreateUIProvider(*this, uiType));


    //devInfo_.driverInst = boost::make_shared<AuroraVisionDrv>();
    //devInfo_.driverInst = boost::make_shared<AuroraVisionDrv>(AURORAVISONDRV_SENSOR_TYPE_ONBOARD_MIPI_IMU);
    devInfo_.driverInst = boost::make_shared<AuroraVisionDrv>(AURORAVISONDRV_SENSOR_TYPE_ONBOARD_IMU);

    devInfo_.driverInst->addListener(AuroraVisionDrv::AURORAVISONDRV_SENSOR_EVENT, &GrabberApplication::onEvent, this);
    
    do {
#if 0
        {
            rp::device::usb::DeviceList matchedDevList;
            rp::device::usb::Device::FindDevices(AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_VID, AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_PID, matchedDevList);

            if (param_.opt_deviceID >= matchedDevList.size()) {
                fprintf(stderr, "Cannot find the specified camera device with id %d.\n", param_.opt_deviceID);
                break;
            }
            ans = devInfo_.driverInst->bindToUSBDevice(matchedDevList[param_.opt_deviceID]);

        }

        if (IS_FAIL(ans)) {
            fprintf(stderr, "failed to binded to the device with id %d, code %x.\n", param_.opt_deviceID, ans);
            break;
        }
#else
        ans = devInfo_.driverInst->openOnboardSensors();
        if (IS_FAIL(ans))
        {
            fprintf(stderr, "failed to open openOnboardSensors, code %x.\n", ans);
            break;
        }


#endif
        auto& streamConfig = devInfo_.cachedStreamConfig;

        if (IS_FAIL(devInfo_.driverInst->getCachedStreamConfig(streamConfig))) {
            fprintf(stderr, "failed to retrieve the stream config.\n");
            break;
        }

        ui_->onNewDeviceConnected(this->devInfo_);


        if (!enableStreamAndSelectProfile(streamConfig, param_.opt_profileID)) {
            break;
        }

        for (auto& itr : enabledImageStreams_)
        {
            ui_->onImageStreamChanged(itr.first, itr.second, true);
        }

        for (auto& itr : enabledIMUStreams_)
        {
            ui_->onIMUStreamChanged(itr.first, itr.second, true);
        }

        dataRecorder_ = boost::shared_ptr<DataRecorder>(DataRecorder::CreateDataRecorder((DataRecorder::RecorderType)param_.opt_recorderType));
        if (!dataRecorder_) break;

        
        fillStreamInfoToRecorder(dataRecorder_);

        
        // prepare for recording ...
        auto origFilename = boost::filesystem::path(param_.opt_recordFolder) / param_.opt_recordName;

        string proposedRecordFile;
        if (!dataRecorder_->proposeRecordingFile(origFilename.string().c_str(), proposedRecordFile))
        {
            fprintf(stderr, "The specified record name %s cannot be used.\n", param_.opt_recordName.c_str());
            break;
        }

        printf("Data will be stored to the file path %s\n", proposedRecordFile.c_str());

        if (!dataRecorder_->startRecordingSession(proposedRecordFile.c_str())) {
            fprintf(stderr, "Cannot create the record entity.\n");
            break;
        }
        
        printSelectedStreamInfo();
        delay(1000);

        ui_->onStartPreview();


        printf("Start streaming ... \n");

        if (IS_FAIL(ans = devInfo_.driverInst->startStreaming())) {
            fprintf(stderr, "Error, cannot start the streaming process.\n");
            break;
        }

        printf("Recording ... \n");


 
        isInited_ = true;
        return true;
    } while (0);
    
    dataRecorder_ = nullptr;
    ui_ = nullptr;
    devInfo_.driverInst = nullptr;
	return false;
}

void GrabberApplication::requireExit()
{
    requireExit_ = true;
}

void GrabberApplication::onExit()
{
    if (!isInited_) {
        return;
    }

    if (ui_) {
        ui_->onStopPreview();
    }

    if (devInfo_.driverInst) {
        devInfo_.driverInst->stopStreaming();
        devInfo_.driverInst->removeAllListeners();
        devInfo_.driverInst->unbind();
    }


    if (dataRecorder_) {
        dataRecorder_->stopRecordingSession();
    }

    if (ui_) {
        ui_->onDestroyUI();
    }


    ui_ = nullptr;
    dataRecorder_ = nullptr;
    devInfo_.driverInst = nullptr;
    isInited_ = false;
}

bool GrabberApplication::operationTick()
{
    if (requireExit_)  return false;

    if (!ui_->onUITick()) return false;

    return true;
}

void GrabberApplication::fillStreamInfoToRecorder(boost::shared_ptr<rp::auroracore::tools::DataRecorder> recorder)
{
    auto& streamConfig = devInfo_.cachedStreamConfig;
    for (auto& itr : enabledImageStreams_)
    {
        recorder->setImageStreamDesc(itr.first, *streamConfig.allEventStreams[itr.first].profiles[itr.second].as<auroravision_imagestream_desc_t>());
    }

    for (auto& itr : enabledIMUStreams_)
    {
        recorder->setIMUStreamDesc(itr.first, *streamConfig.allEventStreams[itr.first].profiles[itr.second].as<auroravision_imustream_desc_t>());
    }
}

bool GrabberApplication::enableStreamAndSelectProfile(const AuroraVision_DeviceConfiguration& streamConfig, int desiredImageProfileID)
{
    u_result ans;
    enabledImageStreams_.clear();
    enabledIMUStreams_.clear();

    auto& drvInst = devInfo_.driverInst;
    for (size_t streamID = 0; streamID < streamConfig.allEventStreams.size(); ++streamID)
    {
        const auto& currentStreamDesc = streamConfig.allEventStreams[streamID];

        switch (currentStreamDesc->stream_event_type)
        {
        case AURORAVISION_STREAM_EVENT_TYPE_IMAGE:
        {

            if (desiredImageProfileID == -1) {
                desiredImageProfileID = 0;
                //find the profile  with the highest fps
                _u32 desiredFPS = 0;
                for (size_t id = 0; id < currentStreamDesc.profiles->size(); ++id) {
                    if (desiredFPS < currentStreamDesc.profiles[id].as<auroravision_imagestream_desc_t>()->fps) {
                        desiredFPS = currentStreamDesc.profiles[id].as<auroravision_imagestream_desc_t>()->fps;
                        desiredImageProfileID = id;
                    }
                }
            }
            else {
                if (desiredImageProfileID >= currentStreamDesc.profiles->size()) {
                    fprintf(stderr, "Error, the specified profile with ID %d doesn't exist.\n", desiredImageProfileID);
                    return false;
                }
            }


            _u32 imageType = currentStreamDesc.profiles[desiredImageProfileID].as<auroravision_imagestream_desc_t>()->image_type;

            switch (imageType & AURORAVISION_IMAGESTREAM_TYPE_BASE_FORMAT_MASK) {
            case AURORAVISION_IMAGESTREAM_TYPE_MONO8:
            case AURORAVISION_IMAGESTREAM_TYPE_BAYER8:
            case AURORAVISION_IMAGESTREAM_TYPE_Y10:
                break;
            default:
                fprintf(stderr, "Error, supported image type %x for stream %u.\n", imageType, streamID);
                return false;
            }

            if (IS_FAIL(ans = drvInst->setActiveStreamProfile(streamID, desiredImageProfileID)))
            {
                fprintf(stderr, "Error, the image profile ID %d cannot be enabled for the stream id=%u\n", desiredImageProfileID, streamID);
                return false;
            }

            if (IS_FAIL(ans = drvInst->setStreamEnableState(streamID))) {
                fprintf(stderr, "Failed to enable the image event stream with ID %d (0x%x)\n", streamID, ans);
                return false;
            }

            enabledImageStreams_.push_back(std::make_pair((int)streamID, desiredImageProfileID));
            printf("INFO: enabled image stream %d with the profile %d...\n", streamID, desiredImageProfileID);

        }
        break;


        case AURORAVISION_STREAM_EVENT_TYPE_IMU:
        {

            const auto& defaultProfile = currentStreamDesc.defaultProfile.as<auroravision_imustream_desc_t>();
            int profileID = 0;
            if (defaultProfile) {
                profileID = defaultProfile->profile_id;
                drvInst->setActiveStreamProfile(streamID, defaultProfile->profile_id);

                // simply enable it ..
                if (IS_FAIL(ans = drvInst->setStreamEnableState(streamID))) {
                    fprintf(stderr, "Failed to enable the IMU event stream with ID %d (0x%x)", streamID, ans);
                    return false;
                }
            }
            enabledIMUStreams_.push_back(std::make_pair((int)streamID, profileID));

            printf("INFO: enabled imu stream with id %d.\n", streamID);
        }
        break;

        default:
            break;
        }

    }

    return true;
}

static const char * getStreamTypeString(_u8 type)
{
    static const char * typeString[] = {
         "Unknown",
         "Image",
         "IMU",
         "LOC_6DoF"
    };

    if (type >= _countof(typeString)) type = 0;
    return typeString[type];
}

void GrabberApplication::printAllProfiles()
{
    u_result ans;
    //auto drvInst = boost::make_shared<AuroraVisionDrv>();
    auto drvInst = boost::make_shared<AuroraVisionDrv>(AURORAVISONDRV_SENSOR_TYPE_ONBOARD_MIPI_IMU);

#if 0
    {
        rp::device::usb::DeviceList matchedDevList;
        rp::device::usb::Device::FindDevices(AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_VID, AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_PID, matchedDevList);

        if (param_.opt_deviceID >= matchedDevList.size()) {
            fprintf(stderr, "Cannot find the specified camera device with id %d.\n", param_.opt_deviceID);
            return; 
        }
        ans = drvInst->bindToUSBDevice(matchedDevList[param_.opt_deviceID]);

    }

    if (IS_FAIL(ans)) {
        fprintf(stderr, "failed to binded to the device with id %d, code %x.\n", param_.opt_deviceID, ans);
        return;
    }
#endif
    ans = drvInst->openOnboardSensors();
    if (IS_FAIL(ans))
    {
        fprintf(stderr, "failed to open openOnboardSensors, code %x.\n", ans);
        return;
    }


    AuroraVision_DeviceConfiguration devConfig;

    if (IS_FAIL(drvInst->getCachedStreamConfig(devConfig))) {
        fprintf(stderr, "failed to retrieve the stream config.\n");
        return;
    }

    for (size_t streamID = 0; streamID < devConfig.allEventStreams.size(); ++streamID)
    {
        printf("Stream Event ID %d:\n", streamID);
        printf("  name: %s\n", devConfig.allEventStreams[streamID]->description_message);
        printf("  type: %x (%s)\n", devConfig.allEventStreams[streamID]->stream_event_type, getStreamTypeString(devConfig.allEventStreams[streamID]->stream_event_type));
        printf("  packet size: %d bytes\n", (int)devConfig.allEventStreams[streamID]->logic_packet_size);
        printf("  feature bitmap: %x\n", devConfig.allEventStreams[streamID]->event_feature_bitmap);
        printf("\n");
        

        switch (devConfig.allEventStreams[streamID]->stream_event_type)
        {
            case AURORAVISION_STREAM_EVENT_TYPE_IMAGE:
            {
                printf("  profile count: %d\n", devConfig.allEventStreams[streamID].profiles->size());
                printf("  active profile ID: %d\n", devConfig.allEventStreams[streamID].activeProfile.as<auroravision_imagestream_desc_t>() ? devConfig.allEventStreams[streamID].activeProfile.as<auroravision_imagestream_desc_t>()->profile_id:-1);
                printf("  default profile ID: %d\n", devConfig.allEventStreams[streamID].defaultProfile.as<auroravision_imagestream_desc_t>() ? devConfig.allEventStreams[streamID].defaultProfile.as<auroravision_imagestream_desc_t>()->profile_id : -1);
            
                printf("--------------------\n");
                for (size_t profileID = 0; profileID < devConfig.allEventStreams[streamID].profiles->size(); ++profileID)
                {
                    auto profile = devConfig.allEventStreams[streamID].profiles[profileID].as<auroravision_imagestream_desc_t>();
                    printf("    #%d\n", profileID);
                    printf("    %d x %d @ %d fps\n"
                        "    pixel byte size:  %d\n"
                        "    line width     :  %d\n"
                        "    image type     :  %x\n"
                        "    feature flag   :  %x\n\n"
                        , profile->image_cols
                        , profile->image_rows
                        , profile->fps
                        , profile->image_pixel_byte_size
                        , profile->image_line_width
                        , profile->image_type
                        , profile->image_feature_flag
                    );
                }
                printf("--------------------\n");
            }
            break;

            case AURORAVISION_STREAM_EVENT_TYPE_IMU:
            {
                printf("  profile count: %lu\n", devConfig.allEventStreams[streamID].profiles->size());
                printf("  active profile ID: %d\n", devConfig.allEventStreams[streamID].activeProfile.as<auroravision_imustream_desc_t>() ? devConfig.allEventStreams[streamID].activeProfile.as<auroravision_imustream_desc_t>()->profile_id:-1);
                printf("  default profile ID: %d\n", devConfig.allEventStreams[streamID].defaultProfile.as<auroravision_imustream_desc_t>() ? devConfig.allEventStreams[streamID].defaultProfile.as<auroravision_imustream_desc_t>()->profile_id : -1);
            
                printf("--------------------\n");
                for (size_t profileID = 0; profileID < devConfig.allEventStreams[streamID].profiles->size(); ++profileID)
                {
                    auto profile = devConfig.allEventStreams[streamID].profiles[profileID].as<auroravision_imustream_desc_t>();
                    printf("    #%d\n", (int)profileID);
                    printf(
                        "    Sample Rate         :  %d hz\n"
                        "    sensitivity_gyro_dps:  %d dps\n"
                        "    sensitivity_acc_g   :  %d g\n"
                        "    feature flag        :  %x\n\n"
                        , profile->samplerate
                        , profile->sensitivity_gyro_dps
                        , profile->sensitivity_acc_g
                        , profile->feature_flag
                    );
                }
                printf("--------------------\n");
            }
            break;
        }

        printf("\n");
    }
}

void GrabberApplication::printSelectedStreamInfo()
{
    printf("The following stream data will be recorded:\n");
    printf("  Video Streams Count: %u\n", enabledImageStreams_.size());


    auto& streamConfig = devInfo_.cachedStreamConfig;
    for (auto itr : enabledImageStreams_)
    {
        auto profile = streamConfig.allEventStreams[itr.first].profiles[itr.second].as<auroravision_imagestream_desc_t>();
        const char* imageType = (profile->image_type & AURORAVISION_IMAGESTREAM_TYPE_DUAL_FLG) ? "dual" : "single";
        printf("    %d-%d: %s %dx%d @ %d fps : %s\n", itr.first, itr.second, imageType, profile->image_cols, profile->image_rows, profile->fps, streamConfig.allEventStreams[itr.first]->description_message);
    }
    printf("  IMU Streams Count: %u\n", enabledIMUStreams_.size());
    for (auto itr : enabledIMUStreams_)
    {
        auto profile = streamConfig.allEventStreams[itr.first].profiles[itr.second].as<auroravision_imustream_desc_t>();
        printf("    %d-%d: %d Hz %d dps %d g : %s\n", itr.first, itr.second, profile->samplerate, profile->sensitivity_gyro_dps, profile->sensitivity_acc_g, streamConfig.allEventStreams[itr.first]->description_message);
    }
    printf("----------------------------\n");
}

void GrabberApplication::printAllDevices()
{
    rp::device::usb::DeviceList matchedDevList;
    rp::device::usb::Device::FindDevices(AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_VID, AuroraVisionDrv::AURORAVISIONDRV_DEFAULT_PID, matchedDevList);

    printf("Totally %d device(s) have been found.\n", (int)matchedDevList.size());
    for (size_t pos = 0; pos < matchedDevList.size(); ++pos)
    {

        auto devInst = matchedDevList[pos]->openDevice();
        if (!devInst) {
            printf("%d\t<no access>\t<no access>\n", (int)pos);
            continue;
        }
        std::string prodName, vendorName, serialNum;


        if (IS_FAIL(devInst->getProductName(prodName)))
        {
            prodName = "???";
        }

        if (IS_FAIL(devInst->getManufacturerName(vendorName)))
        {
            vendorName = "???";
        }

        if (IS_FAIL(devInst->getSerialString(serialNum)))
        {
            serialNum = "???";
        }
        printf("%d\t%s\t%s\t%s\n", (int)pos, prodName.c_str(), vendorName.c_str(), serialNum.c_str());

        matchedDevList[pos]->closeDevice();
    }

    printf("-------------\n\n");
}



static void AWB(cv::Mat& input)
{
    vector< cv::Mat> imageRGB(3);

    split(input, imageRGB);


    double R, G, B;
    B = mean(imageRGB[0])[0];
    G = mean(imageRGB[1])[0];
    R = mean(imageRGB[2])[0];


    double KR, KG, KB;
    KB = (R + G + B) / (3 * B);
    KG = (R + G + B) / (3 * G);
    KR = (R + G + B) / (3 * R);


    imageRGB[0] = imageRGB[0] * KB;
    imageRGB[1] = imageRGB[1] * KG;
    imageRGB[2] = imageRGB[2] * KR;

    merge(imageRGB, input);
}


void GrabberApplication::onDecodeImageFrameCollection(AuroraVisionEvent message)
{
    if (this->requireExit_) return;


    auto imageFrame = message.getDataAs< ImageFrame>();

#if 1
        static uint64_t last_frame = 0;
        static uint64_t frame_count = 0;

        static uint64_t last_ts_us = 0;
        
        frame_count++;

        const auto start = std::chrono::steady_clock::now();

        //uint64_t current_ts = std::chrono::duration_cast<std::chrono::milliseconds>(start.time_since_epoch()).count();
        uint64_t current_ts_us = imageFrame->device_timestamp/1000;
        if (current_ts_us - last_ts_us >= 2000000)
        {
                printf("image fps: %4.1f\n", frame_count * 1000000.0f / (current_ts_us - last_ts_us));
                frame_count = 0;
                last_ts_us = current_ts_us;
        }
#endif

    const auroravision_imagestream_desc_t* profile = imageFrame->profile.as< auroravision_imagestream_desc_t>();

    _u32 baseImageType = (profile->image_type & AURORAVISION_IMAGESTREAM_TYPE_BASE_FORMAT_MASK);

    switch (baseImageType) {
    case AURORAVISION_IMAGESTREAM_TYPE_MONO8:
    case AURORAVISION_IMAGESTREAM_TYPE_BAYER8:
        break;
    default:
        return;
    }

    if (profile->image_type & AURORAVISION_IMAGESTREAM_TYPE_DUAL_FLG) {
        // dual cam
        void* imageBuffer = imageFrame->data;
        cv::Mat rawSyncedImageFrame((int)profile->image_rows, (int)profile->image_cols, CV_8UC2, imageBuffer);

        std::vector<cv::Mat> splitted(2);
        cv::Mat c0, c1;


        cv::split(rawSyncedImageFrame, splitted);


        if (baseImageType == AURORAVISION_IMAGESTREAM_TYPE_BAYER8)
        {
            cv::Mat colored0, colored1;
            cv::cvtColor(splitted[0], colored0, cv::COLOR_BayerGB2RGB);
            cv::cvtColor(splitted[1], colored1, cv::COLOR_BayerGB2RGB);
            AWB(colored0);
            AWB(colored1);
            c0 = colored0;
            c1 = colored1;
        }
        else {
            c0 = splitted[0];
            c1 = splitted[1];
        }

        ui_->onPushDualImageData(imageFrame->streamID, c0, c1);
        dataRecorder_->pushImageData(imageFrame->streamID, DataRecorder::CAMTYPE_LEFT, c0, imageFrame->device_timestamp);
        dataRecorder_->pushImageData(imageFrame->streamID, DataRecorder::CAMTYPE_RIGHT, c1, imageFrame->device_timestamp);

    }
    else {
        // single cam
        void* imageBuffer = imageFrame->data;
        cv::Mat rawImageFrame((int)profile->image_rows, (int)profile->image_cols, CV_8UC1, imageBuffer);
        cv::Mat c0;


        if (baseImageType == AURORAVISION_IMAGESTREAM_TYPE_BAYER8)
        {
            cv::Mat colored0;
            cv::cvtColor(rawImageFrame, colored0, cv::COLOR_BayerGB2RGB);
            AWB(colored0);
            c0 = colored0;
        }
        else {
            c0 = rawImageFrame;

        }
        ui_->onPushImageData(imageFrame->streamID, c0);
        dataRecorder_->pushImageData(imageFrame->streamID, DataRecorder::CAMTYPE_LEFT, c0, imageFrame->device_timestamp);

    }
}

void GrabberApplication::onDecodeIMUFrame(rp::auroracore::drv::AuroraVisionEvent message)
{
    if (this->requireExit_) return;

    auto imuFrame = message.getDataAs<IMU6oFData>();


    #if 1
        static uint64_t last_frame = 0;
        static uint64_t frame_count = 0;

        static uint64_t last_ts_us = 0;
        
        frame_count++;

        const auto start = std::chrono::steady_clock::now();

        //uint64_t current_ts = std::chrono::duration_cast<std::chrono::milliseconds>(start.time_since_epoch()).count();
        uint64_t current_ts_us = imuFrame->device_timestamp/1000;
        if (current_ts_us - last_ts_us >= 2000000)
        {
                printf("imu fps: %4.1f\n", frame_count * 1000000.0f / (current_ts_us - last_ts_us));
                frame_count = 0;
                last_ts_us = current_ts_us;
        }
#endif


    dataRecorder_->pushIMUData(imuFrame->streamID, *imuFrame, imuFrame->device_timestamp);
    ui_->onPushIMUData(imuFrame->streamID, imuFrame);

}

u_result GrabberApplication::onEvent(_u32 eventid, rp::ev::EventDispatcher<rp::auroracore::drv::AuroraVisionEvent>& sender, rp::auroracore::drv::AuroraVisionEvent message)
{
    switch (message.eventData->eventType)
    {
    case AURORAVISION_EVT_SYNC_IMAGEFRAME_COLLECTION:
    {
        onDecodeImageFrameCollection(message);

    }
    break;
    case AURORAVISION_EVT_IMU_6DOF:
    {
        onDecodeIMUFrame(message);
    }
    break;
    }
    return RESULT_OK;
}

DeviceInfo::DeviceInfo()
{}

DeviceInfo::DeviceInfo(boost::shared_ptr<rp::auroracore::drv::AuroraVisionDrv>& drv, rp::auroracore::drv::AuroraVision_DeviceConfiguration& config)
    : driverInst(drv)
    , cachedStreamConfig(config)
{
}

DeviceInfo::~DeviceInfo()
{
}

}